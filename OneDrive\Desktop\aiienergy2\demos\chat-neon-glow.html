<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Glow Chat Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #000;
            background-image: 
                radial-gradient(circle at 20% 20%, #ff006e 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, #8338ec 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, #3a86ff 0%, transparent 50%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            animation: backgroundShift 8s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { filter: hue-rotate(0deg) brightness(0.8); }
            100% { filter: hue-rotate(30deg) brightness(1); }
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid transparent;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            animation: containerGlow 3s ease-in-out infinite alternate;
        }

        @keyframes containerGlow {
            0% { 
                border-image: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff) 1;
                box-shadow: 0 0 30px rgba(255, 0, 110, 0.5);
            }
            100% { 
                border-image: linear-gradient(45deg, #3a86ff, #ff006e, #8338ec) 1;
                box-shadow: 0 0 50px rgba(58, 134, 255, 0.7);
            }
        }

        .chat-header {
            padding: 20px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: flex-end;
            background: rgba(0, 0, 0, 0.5);
        }

        .close-btn {
            background: transparent;
            border: 2px solid #ff006e;
            color: #ff006e;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            text-shadow: 0 0 10px #ff006e;
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
        }

        .close-btn:hover {
            background: #ff006e;
            color: #000;
            box-shadow: 0 0 30px rgba(255, 0, 110, 0.8);
            transform: scale(1.1);
        }

        .welcome-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .welcome-title {
            font-size: 3rem;
            font-weight: 300;
            color: #fff;
            margin-bottom: 20px;
            text-shadow: 0 0 20px #ff006e, 0 0 40px #ff006e;
            animation: titleGlow 2s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% { 
                text-shadow: 0 0 20px #ff006e, 0 0 40px #ff006e;
                transform: scale(1);
            }
            100% { 
                text-shadow: 0 0 30px #8338ec, 0 0 60px #8338ec;
                transform: scale(1.02);
            }
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 48px;
            max-width: 500px;
            line-height: 1.6;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            width: 100%;
            max-width: 600px;
        }

        .suggestion-card {
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid transparent;
            border-radius: 16px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.4s ease;
            text-align: right;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .suggestion-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff);
            opacity: 0;
            transition: opacity 0.4s ease;
            z-index: -1;
        }

        .suggestion-card:hover {
            border-color: #ff006e;
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 10px 30px rgba(255, 0, 110, 0.4);
        }

        .suggestion-card:hover::before {
            opacity: 0.1;
        }

        .suggestion-card:nth-child(2):hover {
            border-color: #8338ec;
            box-shadow: 0 10px 30px rgba(131, 56, 236, 0.4);
        }

        .suggestion-card:nth-child(3):hover {
            border-color: #3a86ff;
            box-shadow: 0 10px 30px rgba(58, 134, 255, 0.4);
        }

        .suggestion-card:nth-child(4):hover {
            border-color: #ff006e;
            box-shadow: 0 10px 30px rgba(255, 0, 110, 0.4);
        }

        .suggestion-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #fff;
            margin-bottom: 12px;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .suggestion-desc {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
        }

        .input-section {
            padding: 24px;
            background: rgba(0, 0, 0, 0.5);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-container {
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid transparent;
            border-radius: 25px;
            padding: 16px 20px;
            display: flex;
            align-items: flex-end;
            gap: 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .input-container:focus-within {
            border-image: linear-gradient(45deg, #ff006e, #8338ec) 1;
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #fff;
            font-size: 16px;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
            line-height: 1.5;
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-btn {
            background: linear-gradient(45deg, #ff006e, #8338ec);
            border: none;
            color: #fff;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.5);
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(255, 0, 110, 0.8);
        }

        .send-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        .input-tools {
            display: flex;
            gap: 16px;
            margin-top: 16px;
            justify-content: center;
        }

        .tool-btn {
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 18px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
        }

        .tool-btn:hover {
            border-color: #ff006e;
            color: #fff;
            box-shadow: 0 0 15px rgba(255, 0, 110, 0.3);
            transform: translateY(-2px);
        }

        .messages-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            display: none;
        }

        .message {
            margin-bottom: 24px;
            animation: messageGlow 0.5s ease;
        }

        @keyframes messageGlow {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 75%;
            padding: 18px 22px;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
            font-size: 15px;
            line-height: 1.5;
            backdrop-filter: blur(10px);
        }

        .message.user .message-bubble {
            background: rgba(255, 0, 110, 0.2);
            border: 1px solid #ff006e;
            color: #fff;
            border-bottom-left-radius: 8px;
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
        }

        .message.bot .message-bubble {
            background: rgba(131, 56, 236, 0.2);
            border: 1px solid #8338ec;
            color: #fff;
            border-bottom-right-radius: 8px;
            box-shadow: 0 0 20px rgba(131, 56, 236, 0.3);
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 24px;
        }

        .typing-bubble {
            display: inline-block;
            background: rgba(58, 134, 255, 0.2);
            border: 1px solid #3a86ff;
            padding: 18px 22px;
            border-radius: 20px;
            border-bottom-right-radius: 8px;
            color: #fff;
            box-shadow: 0 0 20px rgba(58, 134, 255, 0.3);
        }

        .typing-dots {
            display: inline-flex;
            gap: 6px;
            margin-left: 10px;
        }

        .typing-dot {
            width: 10px;
            height: 10px;
            background: #3a86ff;
            border-radius: 50%;
            animation: typingNeon 1.4s infinite;
            box-shadow: 0 0 10px #3a86ff;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingNeon {
            0%, 60%, 100% { 
                transform: scale(1);
                box-shadow: 0 0 10px #3a86ff;
            }
            30% { 
                transform: scale(1.3);
                box-shadow: 0 0 20px #3a86ff, 0 0 30px #3a86ff;
            }
        }

        .demo-label {
            position: absolute;
            top: 16px;
            left: 24px;
            background: linear-gradient(45deg, #ff006e, #8338ec);
            color: #fff;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 20px rgba(255, 0, 110, 0.5);
        }

        /* Scrollbar */
        .messages-area::-webkit-scrollbar {
            width: 8px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ff006e, #8338ec);
            border-radius: 4px;
            box-shadow: 0 0 10px rgba(255, 0, 110, 0.5);
        }

        @media (max-width: 768px) {
            .suggestions-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 2.5rem;
            }
            
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="demo-label">Neon Glow</div>
        
        <div class="chat-header">
            <button class="close-btn" onclick="window.close()">×</button>
        </div>

        <div class="welcome-section" id="welcomeSection">
            <h1 class="welcome-title">مرحباً</h1>
            <p class="welcome-subtitle">مرحباً بك في عالم الطاقة المضيء - كيف يمكنني إنارة طريقك؟</p>
            
            <div class="suggestions-grid">
                <div class="suggestion-card" data-suggestion="تحليل استهلاك الطاقة">
                    <div class="suggestion-title">تحليل استهلاك الطاقة</div>
                    <div class="suggestion-desc">إضاءة على استهلاكك وكشف نقاط الهدر المخفية</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="نصائح توفير الطاقة">
                    <div class="suggestion-title">نصائح توفير الطاقة</div>
                    <div class="suggestion-desc">أسرار توفير الطاقة التي تضيء مستقبلك</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الطاقة المتجددة">
                    <div class="suggestion-title">الطاقة المتجددة</div>
                    <div class="suggestion-desc">اكتشف مصادر الطاقة التي لا تنضب</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الأجهزة الذكية">
                    <div class="suggestion-title">الأجهزة الذكية</div>
                    <div class="suggestion-desc">تقنيات مضيئة لمنزل ذكي وموفر</div>
                </div>
            </div>
        </div>

        <div class="messages-area" id="messagesArea"></div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                يضيء الأفكار...
            </div>
        </div>

        <div class="input-section">
            <div class="input-container">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="أضئ استفسارك هنا..."
                    rows="1"
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>⚡</button>
            </div>
            
            <div class="input-tools">
                <button class="tool-btn">
                    <span>🎤</span>
                    <span>صوت مضيء</span>
                </button>
                <button class="tool-btn">
                    <span>📎</span>
                    <span>مرفق نيون</span>
                </button>
                <button class="tool-btn">
                    <span>🔍</span>
                    <span>بحث مشع</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        const welcomeSection = document.getElementById('welcomeSection');
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const suggestionCards = document.querySelectorAll('.suggestion-card');

        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            sendBtn.disabled = this.value.trim() === '';
        });

        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        suggestionCards.forEach(card => {
            card.addEventListener('click', function() {
                const suggestion = this.getAttribute('data-suggestion');
                startChat(suggestion);
            });
        });

        function startChat(message) {
            welcomeSection.style.display = 'none';
            messagesArea.style.display = 'block';
            addMessage(message, 'user');
            showTyping();
            
            setTimeout(() => {
                hideTyping();
                const responses = {
                    'تحليل استهلاك الطاقة': '✨ رائع! دعني أضيء لك طريق تحليل الطاقة:\n\n🔍 فحص شامل للاستهلاك\n⚡ تحديد نقاط الهدر المخفية\n📊 تحليل أنماط الاستخدام\n💡 خطة إنارة للتحسين\n🌟 توقعات التوفير المضيئة\n\nهل تريد أن نبدأ بإضاءة فاتورتك الحالية؟',
                    'نصائح توفير الطاقة': '🌟 إليك أسرار الطاقة المضيئة:\n\n💡 LED - نور يدوم ويوفر 80%\n❄️ تكييف ذكي على 24° - برودة موفرة\n🔌 افصل الأشباح الكهربائية\n🌞 استغل نور الشمس الطبيعي\n⏰ مؤقتات ذكية - توفير تلقائي\n\nأي من هذه الأنوار تريد أن نضيئها أكثر؟',
                    'الطاقة المتجددة': '🌞 مرحباً بك في عالم الطاقة اللانهائية!\n\n☀️ الألواح الشمسية:\n- نور مجاني لـ 25+ سنة\n- توفير 70-90% من الفاتورة\n- عائد مضيء خلال 5-7 سنوات\n\n💨 طاقة الرياح المتوهجة\n🔋 تخزين الطاقة المشعة\n\nهل تريد حساب إضاءة التوفير لمنزلك؟',
                    'الأجهزة الذكية': '🤖 مرحباً بك في عصر المنازل المضيئة!\n\n🏠 منظم حرارة ذكي:\n- توفير مشع 10-15%\n- تحكم عن بُعد مضيء\n- ذكاء يتعلم عاداتك\n\n💡 إضاءة ذكية متغيرة\n🔌 مقابس تراقب وتوفر\n📱 تطبيقات تضيء استهلاكك\n\nأي تقنية مضيئة تلفت انتباهك؟'
                };
                
                const response = responses[message] || '✨ سؤال مضيء! دعني أنير لك الطريق بمعلومات قيمة...';
                addMessage(response, 'bot');
            }, 2000);
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            if (welcomeSection.style.display !== 'none') {
                startChat(message);
            } else {
                addMessage(message, 'user');
                showTyping();
                
                setTimeout(() => {
                    hideTyping();
                    addMessage('🌟 استفسار مضيء! دعني أجمع الأنوار لأقدم لك إجابة مشعة...', 'bot');
                }, 1500);
            }

            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.innerHTML = `<div class="message-bubble">${text.replace(/\n/g, '<br>')}</div>`;
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
    </script>
</body>
</html>
