<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Style Chat Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0c1317;
            background-image: 
                radial-gradient(circle at 25% 25%, #1a4d3a 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #2d5a3d 0%, transparent 50%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: #111b21;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .chat-header {
            background: #202c33;
            padding: 20px;
            border-bottom: 1px solid #2a3942;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .avatar {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #25d366, #128c7e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            position: relative;
        }

        .avatar::after {
            content: '';
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #25d366;
            border: 2px solid #202c33;
            border-radius: 50%;
        }

        .chat-info {
            color: #e9edef;
        }

        .chat-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .chat-status {
            font-size: 13px;
            color: #8696a0;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: transparent;
            border: none;
            color: #8696a0;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #2a3942;
            color: #e9edef;
        }

        .messages-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #0b141a;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%23ffffff" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .message {
            margin-bottom: 15px;
            animation: messageSlide 0.3s ease;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            position: relative;
            word-wrap: break-word;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-bubble {
            background: #005c4b;
            color: #e9edef;
            border-bottom-left-radius: 3px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .message.bot .message-bubble {
            background: #202c33;
            color: #e9edef;
            border-bottom-right-radius: 3px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .message-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 5px;
            margin-top: 5px;
            font-size: 11px;
            color: #8696a0;
        }

        .message.user .message-footer {
            justify-content: flex-start;
        }

        .message-time {
            font-size: 11px;
            color: rgba(233, 237, 239, 0.6);
            margin-top: 5px;
            text-align: right;
        }

        .message.user .message-time {
            text-align: left;
        }

        .read-status {
            font-size: 16px;
            color: #53bdeb;
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 15px 20px;
        }

        .typing-bubble {
            display: inline-block;
            background: #202c33;
            padding: 12px 16px;
            border-radius: 12px;
            border-bottom-right-radius: 3px;
            color: #e9edef;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .typing-dots {
            display: inline-flex;
            gap: 3px;
            margin-right: 8px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #8696a0;
            border-radius: 50%;
            animation: typingWave 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingWave {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-8px); }
        }

        .input-area {
            background: #202c33;
            padding: 15px 20px;
            border-top: 1px solid #2a3942;
        }

        .input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-container {
            flex: 1;
            background: #2a3942;
            border-radius: 25px;
            display: flex;
            align-items: flex-end;
            padding: 8px 15px;
            min-height: 45px;
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #e9edef;
            font-size: 15px;
            resize: none;
            max-height: 100px;
            min-height: 20px;
            outline: none;
            padding: 8px 0;
            font-family: inherit;
        }

        .message-input::placeholder {
            color: #8696a0;
        }

        .emoji-btn {
            background: transparent;
            border: none;
            color: #8696a0;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            margin-left: 5px;
        }

        .send-btn {
            background: #25d366;
            border: none;
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
        }

        .send-btn:hover {
            background: #20b858;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Scrollbar Styling */
        .messages-area::-webkit-scrollbar {
            width: 6px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: transparent;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: #2a3942;
            border-radius: 3px;
        }

        .demo-label {
            position: absolute;
            top: 10px;
            left: 20px;
            background: rgba(37, 211, 102, 0.9);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
        }

        .online-indicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 10px;
            }
            
            .message-bubble {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="demo-label">WhatsApp Style Demo</div>
        
        <div class="chat-header">
            <div class="header-info">
                <div class="avatar">⚡</div>
                <div class="chat-info">
                    <div class="chat-name">مساعد الطاقة الذكي</div>
                    <div class="chat-status online-indicator">متصل الآن</div>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-btn">📞</button>
                <button class="action-btn">📹</button>
                <button class="action-btn" onclick="window.close()">×</button>
            </div>
        </div>

        <div class="messages-area" id="messagesArea">
            <div class="message bot">
                <div class="message-bubble">
                    مرحباً! أنا مساعدك الذكي في مجال الطاقة 🌟
                    <br>كيف يمكنني مساعدتك في توفير الطاقة اليوم؟
                </div>
                <div class="message-time">الآن</div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                يكتب...
            </div>
        </div>

        <div class="input-area">
            <div class="input-wrapper">
                <div class="input-container">
                    <button class="emoji-btn">😊</button>
                    <textarea 
                        class="message-input" 
                        id="messageInput" 
                        placeholder="اكتب رسالة..."
                        rows="1"
                    ></textarea>
                </div>
                <button class="send-btn" id="sendBtn" disabled>
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            
            sendBtn.disabled = this.value.trim() === '';
        });

        // Send message on Enter
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;

            // Show typing indicator
            showTyping();

            // Simulate bot response
            setTimeout(() => {
                hideTyping();
                const responses = [
                    'ممتاز! يمكنني مساعدتك في تحليل استهلاك الطاقة وتقديم حلول مخصصة 💡',
                    'رائع! إليك أفضل النصائح لتوفير الطاقة في منزلك أو مكتبك ⚡',
                    'بالتأكيد! سأقدم لك خطة شاملة لتحسين كفاءة الطاقة 🌱',
                    'هذا سؤال ممتاز! دعني أشارك معك أحدث التقنيات في توفير الطاقة 🔋'
                ];
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage(randomResponse, 'bot');
            }, 2000);
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            const readStatus = sender === 'user' ? '<span class="read-status">✓✓</span>' : '';

            messageDiv.innerHTML = `
                <div class="message-bubble">${text}</div>
                <div class="message-footer">
                    <span class="message-time">${timeString}</span>
                    ${readStatus}
                </div>
            `;

            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        // Add demo messages
        setTimeout(() => {
            addMessage('مرحباً، أريد نصائح لتوفير الكهرباء في المنزل', 'user');
        }, 1000);

        setTimeout(() => {
            showTyping();
        }, 2000);

        setTimeout(() => {
            hideTyping();
            addMessage('أهلاً وسهلاً! إليك أهم النصائح لتوفير الكهرباء:\n\n💡 استخدم مصابيح LED\n❄️ اضبط التكييف على 24 درجة\n🔌 افصل الأجهزة غير المستخدمة\n🌞 استفد من الإضاءة الطبيعية\n⚡ اختر أجهزة موفرة للطاقة', 'bot');
        }, 4000);
    </script>
</body>
</html>
