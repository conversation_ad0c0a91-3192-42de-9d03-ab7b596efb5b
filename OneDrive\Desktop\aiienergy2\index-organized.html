<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="description" content="Energy.AI - Smart Energy Solutions with AI-powered optimization. Reduce energy costs by 15-30% with our intelligent energy management systems. Advanced renewable energy solutions for businesses and homes in Jordan and MENA region.">
    <meta name="keywords" content="energy AI, smart energy solutions, renewable energy, energy optimization, artificial intelligence, energy management, cost reduction, sustainability, Jordan energy, MENA energy solutions, solar power, wind energy, smart grid, IoT energy, energy analytics, carbon footprint reduction">
    <meta name="author" content="Energy.AI - Mohammad <PERSON>">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="language" content="en, ar">
    <meta name="theme-color" content="#1976d2">
    <meta name="msapplication-TileColor" content="#1976d2">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Energy.AI">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://energy-ai.netlify.app/">
    <meta property="og:title" content="Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization">
    <meta property="og:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Smart energy management for businesses and homes. Advanced renewable energy solutions with real-time monitoring and predictive analytics.">
    <meta property="og:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta property="og:image:width" content="512">
    <meta property="og:image:height" content="512">
    <meta property="og:image:alt" content="Energy.AI Logo - Smart Energy Solutions">
    <meta property="og:site_name" content="Energy.AI">
    <meta property="og:locale" content="en_US">
    <meta property="og:locale:alternate" content="ar_JO">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://energy-ai.netlify.app/">
    <meta name="twitter:title" content="Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization">
    <meta name="twitter:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Advanced renewable energy management systems.">
    <meta name="twitter:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta name="twitter:image:alt" content="Energy.AI - Smart Energy Solutions">
    <meta name="twitter:creator" content="@EnergyAI_Jordan">
    <meta name="twitter:site" content="@EnergyAI_Jordan">

    <!-- Canonical and Alternate Languages -->
    <link rel="canonical" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="en" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="ar" href="https://energy-ai.netlify.app/?lang=ar">
    <link rel="alternate" hreflang="x-default" href="https://energy-ai.netlify.app/">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icon-192x192.png">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">

    <title>Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization</title>

    <!-- Critical CSS - Inline for performance -->
    <style>
        /* Critical above-the-fold styles */
        :root {
            --primary-color: #1976d2;
            --secondary-color: #ff7200;
            --background-primary: #0a0a0a;
            --text-primary: #ffffff;
            --transition-fast: 0.15s ease;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', sans-serif;
            background: var(--background-primary);
            color: var(--text-primary);
            overflow-x: hidden;
        }

        .main {
            min-height: 100vh;
            position: relative;
        }

        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: var(--transition-fast);
        }

        /* Critical inline styles for immediate loading */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--background-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.8s ease;
            overflow: hidden;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* Error notification styles */
        .error-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            max-width: 400px;
        }

        .error-content h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }

        .error-content p {
            margin: 0 0 15px 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .error-actions {
            display: flex;
            gap: 10px;
        }

        .error-actions button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: #1976d2;
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 1px solid white;
        }
    </style>

    <!-- Main CSS - Organized and modular -->
    <link rel="stylesheet" href="src/styles/main.css">

    <!-- Critical CSS - Load immediately -->
    <link rel="stylesheet" href="css/welcome-screen.css">

    <!-- Fallback for browsers that don't support ES modules -->
    <noscript>
        <link rel="stylesheet" href="css/styles.css">
        <link rel="stylesheet" href="css/space-background.css">
        <link rel="stylesheet" href="css/icons.css">
        <link rel="stylesheet" href="css/auth.css">
        <link rel="stylesheet" href="css/naya.css">
        <link rel="stylesheet" href="css/enhanced-map-animations.css">
        <link rel="stylesheet" href="css/neon-cursor.css">
    </noscript>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Ionicons -->
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>
</head>
<body>
    <!-- Welcome Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="welcome-logo-section">
                <div class="welcome-logo">
                    <div class="site-logo large-logo"></div>
                    <div class="logo-glow"></div>
                </div>
                <h1 class="welcome-title" data-en="مرحباً" data-ar="مرحباً">مرحباً</h1>
                <h2 class="brand-name" data-en="Energy.AI" data-ar="Energy.AI">Energy.AI</h2>
                <p class="welcome-subtitle" data-en="Smart Energy Solutions Powered by AI" data-ar="حلول الطاقة الذكية مدعومة بالذكاء الاصطناعي">Smart Energy Solutions Powered by AI</p>
            </div>
            <div class="welcome-animation">
                <div class="energy-particles"></div>
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main">
        <!-- Navigation Bar -->
        <nav class="navbar">
            <div class="icon">
                <div class="site-logo"></div>
                <h2 class="logo">Energy.Ai</h2>
            </div>

            <div class="menu">
                <ul>
                    <li><a href="#home" data-en="home">HOME</a></li>
                    <li><a href="#about" data-en="about">ABOUT</a></li>
                    <li><a href="#service" id="serviceLink" data-en="services">SERVICES</a></li>
                    <li><a href="#design" data-en="design">DESIGN</a></li>
                    <li><a href="#contact" data-en="contact">CONTACT</a></li>
                </ul>
            </div>

            <div class="language-toggle">
                <button class="lang-btn" data-lang="en">English</button>
                <button class="lang-btn" data-lang="ar">العربية</button>
            </div>

            <div class="theme-switch-wrapper">
                <label class="theme-switch" for="checkbox">
                    <input type="checkbox" id="checkbox" />
                    <div class="slider round"></div>
                </label>
            </div>

            <!-- Mobile menu toggle -->
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="content section" id="home">
            <div class="container">
                <h1>Web Design & <span>Development</span> Energy</h1>
                <p class="par">AI is the spark igniting a new era of energy innovation<br> powering tomorrow with<br>intelligent solutions today</p>

                <button class="cn btn btn-primary" id="joinBtn" data-en="join-us" data-ar="انضم إلينا">Join Us</button>

                <!-- Fixed CTA Button -->
                <div class="fixed-cta-container">
                    <button class="fixed-cta-btn btn btn-secondary" id="fixedCtaBtn" data-en="get-free-consultation" data-ar="احصل على استشارة مجانية">
                        <ion-icon name="call-outline"></ion-icon>
                        <span>Get Free Consultation</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="section about-section">
            <div class="container">
                <h2>About Energy.AI</h2>
                <div class="about-content">
                    <p>Energy.AI is at the forefront of combining artificial intelligence with energy solutions to create a sustainable future. Our innovative approaches help businesses and individuals optimize their energy usage, reduce costs, and minimize environmental impact.</p>
                    <div class="features grid grid-cols-3">
                        <div class="feature">
                            <div class="feature-icon-wrapper">
                                <ion-icon name="flash-outline"></ion-icon>
                            </div>
                            <h3>Smart Energy Management</h3>
                            <p>AI-powered solutions to optimize energy consumption in real-time.</p>
                        </div>
                        <div class="feature">
                            <div class="feature-icon-wrapper">
                                <ion-icon name="leaf-outline"></ion-icon>
                            </div>
                            <h3>Sustainable Solutions</h3>
                            <p>Eco-friendly approaches to energy production and distribution.</p>
                        </div>
                        <div class="feature">
                            <div class="feature-icon-wrapper">
                                <ion-icon name="analytics-outline"></ion-icon>
                            </div>
                            <h3>Data-Driven Insights</h3>
                            <p>Comprehensive analytics to make informed energy decisions.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="service" class="section service-section">
            <div class="container">
                <h2>Our Services</h2>
                <div class="service-content">
                    <div class="service-cards grid grid-cols-2">
                        <div class="service-card">
                            <div class="service-icon">
                                <ion-icon name="bulb-outline"></ion-icon>
                            </div>
                            <h3>Energy Optimization</h3>
                            <p>Our AI algorithms analyze your energy consumption patterns and suggest optimizations to reduce waste and cost.</p>
                        </div>
                        <div class="service-card">
                            <div class="service-icon">
                                <ion-icon name="trending-up-outline"></ion-icon>
                            </div>
                            <h3>Predictive Maintenance</h3>
                            <p>Prevent equipment failures before they happen with our predictive maintenance solutions powered by machine learning.</p>
                        </div>
                        <div class="service-card">
                            <div class="service-icon">
                                <ion-icon name="shield-outline"></ion-icon>
                            </div>
                            <h3>Energy Security</h3>
                            <p>Protect your energy infrastructure with advanced threat detection and response systems.</p>
                        </div>
                        <div class="service-card">
                            <div class="service-icon">
                                <ion-icon name="cloud-outline"></ion-icon>
                            </div>
                            <h3>Cloud Energy Management</h3>
                            <p>Access your energy data and controls from anywhere with our secure cloud-based platform.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="section contact-section">
            <div class="container">
                <div class="contact-header">
                    <h2>Get In Touch</h2>
                    <p class="contact-subtitle">Ready to transform your energy future? Let's start the conversation.</p>
                </div>

                <div class="contact-container grid grid-cols-2">
                    <div class="contact-form-wrapper">
                        <div class="contact-form">
                            <h3>Send us a Message</h3>
                            <form id="contactForm">
                                <div class="form-group">
                                    <label for="contact-name">
                                        <ion-icon name="person-outline"></ion-icon>
                                        Full Name
                                    </label>
                                    <input type="text" id="contact-name" placeholder="Enter your full name" required>
                                </div>

                                <div class="form-group">
                                    <label for="contact-email">
                                        <ion-icon name="mail-outline"></ion-icon>
                                        Email Address
                                    </label>
                                    <input type="email" id="contact-email" placeholder="<EMAIL>" required>
                                </div>

                                <div class="form-group">
                                    <label for="contact-message">
                                        <ion-icon name="document-text-outline"></ion-icon>
                                        Message
                                    </label>
                                    <textarea id="contact-message" rows="6" placeholder="Tell us about your energy needs..." required></textarea>
                                </div>

                                <button type="submit" class="submit-btn btn btn-primary" id="contactSubmitBtn">
                                    <span class="btn-text">Send Message</span>
                                    <ion-icon name="paper-plane-outline"></ion-icon>
                                </button>
                                <div id="contactFormStatus" class="form-status"></div>
                            </form>
                        </div>
                    </div>

                    <div class="contact-info-wrapper">
                        <div class="contact-info">
                            <h3>Contact Information</h3>
                            <p class="info-description">Connect with our energy experts today</p>

                            <div class="info-items">
                                <div class="info-item">
                                    <div class="info-icon">
                                        <ion-icon name="location-outline"></ion-icon>
                                    </div>
                                    <div class="info-content">
                                        <h4>Office Location</h4>
                                        <p>Amman, Jordan</p>
                                        <span>Middle East Headquarters</span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-icon">
                                        <ion-icon name="mail-outline"></ion-icon>
                                    </div>
                                    <div class="info-content">
                                        <h4>Email Address</h4>
                                        <p><EMAIL></p>
                                        <span>We reply within 24 hours</span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-icon">
                                        <ion-icon name="call-outline"></ion-icon>
                                    </div>
                                    <div class="info-content">
                                        <h4>Phone Number</h4>
                                        <p>+962 79 155 6430</p>
                                        <span>Mon - Fri, 9:00 AM - 6:00 PM</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Main JavaScript - Organized and modular -->
    <script type="module" src="src/scripts/main.js"></script>
    
    <!-- Fallback for browsers that don't support ES modules -->
    <script nomodule src="js/main.js"></script>
    
    <!-- Service Worker for PWA -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
