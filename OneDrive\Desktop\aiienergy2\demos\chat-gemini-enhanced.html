<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Gemini Style Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0d1117;
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: rgba(13, 17, 23, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            border: 1px solid rgba(48, 54, 61, 0.8);
            box-shadow: 
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .chat-header {
            padding: 20px 24px;
            border-bottom: 1px solid rgba(48, 54, 61, 0.5);
            display: flex;
            justify-content: flex-end;
            background: rgba(13, 17, 23, 0.8);
        }

        .close-btn {
            background: transparent;
            border: none;
            color: #8b949e;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: rgba(48, 54, 61, 0.5);
            color: #f0f6fc;
        }

        .welcome-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 400;
            color: #f0f6fc;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #58a6ff, #a5a6ff, #ff7eb6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-subtitle {
            font-size: 1.1rem;
            color: #8b949e;
            margin-bottom: 48px;
            max-width: 500px;
            line-height: 1.5;
        }

        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            width: 100%;
            max-width: 600px;
            margin-bottom: 40px;
        }

        .suggestion-card {
            background: rgba(21, 32, 43, 0.8);
            border: 1px solid rgba(48, 54, 61, 0.6);
            border-radius: 16px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: right;
            position: relative;
            overflow: hidden;
        }

        .suggestion-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(88, 166, 255, 0.1), rgba(255, 126, 182, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .suggestion-card:hover {
            background: rgba(30, 41, 59, 0.9);
            border-color: rgba(88, 166, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(88, 166, 255, 0.15);
        }

        .suggestion-card:hover::before {
            opacity: 1;
        }

        .suggestion-title {
            font-size: 1rem;
            font-weight: 500;
            color: #f0f6fc;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .suggestion-desc {
            font-size: 0.875rem;
            color: #8b949e;
            line-height: 1.4;
            position: relative;
            z-index: 1;
        }

        .input-section {
            padding: 24px;
            background: rgba(13, 17, 23, 0.9);
            border-top: 1px solid rgba(48, 54, 61, 0.5);
        }

        .input-container {
            background: rgba(21, 32, 43, 0.8);
            border: 1px solid rgba(48, 54, 61, 0.6);
            border-radius: 24px;
            padding: 16px 20px;
            display: flex;
            align-items: flex-end;
            gap: 12px;
            transition: all 0.3s ease;
            position: relative;
        }

        .input-container:focus-within {
            border-color: rgba(88, 166, 255, 0.5);
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
        }

        .message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #f0f6fc;
            font-size: 16px;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
            line-height: 1.5;
        }

        .message-input::placeholder {
            color: #6e7681;
        }

        .send-btn {
            background: linear-gradient(135deg, #58a6ff, #a5a6ff);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(88, 166, 255, 0.3);
        }

        .send-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(88, 166, 255, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        .input-tools {
            display: flex;
            gap: 16px;
            margin-top: 16px;
            justify-content: center;
        }

        .tool-btn {
            background: rgba(21, 32, 43, 0.6);
            border: 1px solid rgba(48, 54, 61, 0.4);
            color: #8b949e;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tool-btn:hover {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(88, 166, 255, 0.3);
            color: #f0f6fc;
        }

        .messages-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            display: none;
        }

        .message {
            margin-bottom: 24px;
            animation: messageSlide 0.4s ease;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 75%;
            padding: 16px 20px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
            font-size: 15px;
            line-height: 1.5;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #58a6ff, #a5a6ff);
            color: white;
            border-bottom-left-radius: 6px;
        }

        .message.bot .message-bubble {
            background: rgba(21, 32, 43, 0.8);
            color: #f0f6fc;
            border: 1px solid rgba(48, 54, 61, 0.6);
            border-bottom-right-radius: 6px;
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 24px;
        }

        .typing-bubble {
            display: inline-block;
            background: rgba(21, 32, 43, 0.8);
            border: 1px solid rgba(48, 54, 61, 0.6);
            padding: 16px 20px;
            border-radius: 18px;
            border-bottom-right-radius: 6px;
            color: #8b949e;
        }

        .typing-dots {
            display: inline-flex;
            gap: 4px;
            margin-left: 8px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #58a6ff;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); opacity: 0.4; }
            30% { transform: translateY(-10px); opacity: 1; }
        }

        .demo-label {
            position: absolute;
            top: 16px;
            left: 24px;
            background: rgba(88, 166, 255, 0.2);
            color: #58a6ff;
            padding: 6px 16px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
            border: 1px solid rgba(88, 166, 255, 0.3);
        }

        /* Scrollbar */
        .messages-area::-webkit-scrollbar {
            width: 8px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: rgba(21, 32, 43, 0.5);
            border-radius: 4px;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: rgba(88, 166, 255, 0.3);
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .suggestions-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="demo-label">Enhanced Gemini Style</div>
        
        <div class="chat-header">
            <button class="close-btn" onclick="window.close()">✕</button>
        </div>

        <div class="welcome-section" id="welcomeSection">
            <h1 class="welcome-title">مرحباً</h1>
            <p class="welcome-subtitle">كيف يمكنني مساعدتك في تحسين استهلاك الطاقة اليوم؟</p>
            
            <div class="suggestions-grid">
                <div class="suggestion-card" data-suggestion="تحليل استهلاك الطاقة">
                    <div class="suggestion-title">تحليل استهلاك الطاقة</div>
                    <div class="suggestion-desc">احصل على تحليل مفصل لاستهلاك الطاقة في منزلك أو مكتبك</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="نصائح توفير الطاقة">
                    <div class="suggestion-title">نصائح توفير الطاقة</div>
                    <div class="suggestion-desc">اكتشف طرق ذكية لتوفير الطاقة وتقليل الفواتير</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الطاقة المتجددة">
                    <div class="suggestion-title">الطاقة المتجددة</div>
                    <div class="suggestion-desc">تعرف على حلول الطاقة المستدامة والألواح الشمسية</div>
                </div>
                
                <div class="suggestion-card" data-suggestion="الأجهزة الذكية">
                    <div class="suggestion-title">الأجهزة الذكية</div>
                    <div class="suggestion-desc">كيفية استخدام التكنولوجيا الذكية لتحسين كفاءة الطاقة</div>
                </div>
            </div>
        </div>

        <div class="messages-area" id="messagesArea"></div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                يحلل البيانات...
            </div>
        </div>

        <div class="input-section">
            <div class="input-container">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="اكتب رسالتك هنا..."
                    rows="1"
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
            
            <div class="input-tools">
                <button class="tool-btn">
                    <span>🎤</span>
                    <span>التحدث الصوتي</span>
                </button>
                <button class="tool-btn">
                    <span>📎</span>
                    <span>Canvas</span>
                </button>
                <button class="tool-btn">
                    <span>🔍</span>
                    <span>بحث</span>
                </button>
                <button class="tool-btn">
                    <span>🧠</span>
                    <span>Deep Research</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        const welcomeSection = document.getElementById('welcomeSection');
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const suggestionCards = document.querySelectorAll('.suggestion-card');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            sendBtn.disabled = this.value.trim() === '';
        });

        // Send message on Enter
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        // Suggestion cards
        suggestionCards.forEach(card => {
            card.addEventListener('click', function() {
                const suggestion = this.getAttribute('data-suggestion');
                startChat(suggestion);
            });
        });

        function startChat(message) {
            // Hide welcome section and show messages area
            welcomeSection.style.display = 'none';
            messagesArea.style.display = 'block';
            
            // Add user message
            addMessage(message, 'user');
            
            // Show typing indicator
            showTyping();
            
            // Simulate bot response
            setTimeout(() => {
                hideTyping();
                const responses = {
                    'تحليل استهلاك الطاقة': 'ممتاز! يمكنني مساعدتك في تحليل استهلاك الطاقة. إليك الخطوات:\n\n🔍 تحليل الفواتير الشهرية\n📊 قياس استهلاك الأجهزة\n💡 تحديد نقاط الهدر\n📈 وضع خطة للتحسين\n\nهل تريد البدء بتحليل فاتورة معينة؟',
                    'نصائح توفير الطاقة': 'إليك أهم النصائح لتوفير الطاقة:\n\n💡 استخدم مصابيح LED (توفر 80% من الطاقة)\n❄️ اضبط التكييف على 24°م\n🔌 افصل الأجهزة غير المستخدمة\n🌞 استفد من الإضاءة الطبيعية\n⚡ اختر أجهزة بتصنيف طاقة عالي\n\nأي من هذه النصائح تريد التفاصيل عنها؟',
                    'الطاقة المتجددة': 'الطاقة المتجددة هي المستقبل! إليك الخيارات المتاحة:\n\n☀️ الألواح الشمسية (ROI خلال 5-7 سنوات)\n💨 طاقة الرياح للمناطق المناسبة\n🌊 الطاقة المائية\n🌱 الطاقة الحيوية\n\nفي الأردن، الألواح الشمسية هي الخيار الأمثل. هل تريد حساب التكلفة والعائد؟',
                    'الأجهزة الذكية': 'الأجهزة الذكية تحدث ثورة في توفير الطاقة:\n\n🏠 منظمات الحرارة الذكية\n💡 مصابيح LED قابلة للتحكم\n🔌 مقابس ذكية لمراقبة الاستهلاك\n📱 تطبيقات إدارة الطاقة\n🤖 أنظمة الأتمتة المنزلية\n\nهل تريد توصيات لأجهزة معينة؟'
                };
                
                const response = responses[message] || 'شكراً لسؤالك! يمكنني مساعدتك في جميع مواضيع الطاقة. ما الذي تريد معرفته تحديداً؟';
                addMessage(response, 'bot');
            }, 2000);
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            if (welcomeSection.style.display !== 'none') {
                startChat(message);
            } else {
                addMessage(message, 'user');
                showTyping();
                
                setTimeout(() => {
                    hideTyping();
                    const responses = [
                        'ممتاز! هذا سؤال مهم في مجال الطاقة. دعني أقدم لك معلومات مفصلة...',
                        'رائع! يمكنني مساعدتك في هذا الموضوع بناءً على أحدث التقنيات...',
                        'بالتأكيد! إليك تحليل شامل لهذا الموضوع مع حلول عملية...',
                        'هذا موضوع مثير للاهتمام! دعني أشارك معك أفضل الممارسات...'
                    ];
                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                    addMessage(randomResponse, 'bot');
                }, 1500);
            }

            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            messageDiv.innerHTML = `
                <div class="message-bubble">${text.replace(/\n/g, '<br>')}</div>
            `;

            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
    </script>
</body>
</html>
