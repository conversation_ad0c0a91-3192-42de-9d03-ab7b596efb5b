<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glassmorphism Chat Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            left: 70%;
            animation-delay: 5s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            left: 40%;
            animation-delay: 10s;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            z-index: 10;
        }

        .chat-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            color: rgba(255, 255, 255, 0.9);
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .glow-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
            to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.8); }
        }

        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .messages-area {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            background: transparent;
        }

        .message {
            margin-bottom: 25px;
            animation: slideInGlass 0.5s ease;
        }

        @keyframes slideInGlass {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .message.user {
            text-align: left;
        }

        .message.bot {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 75%;
            padding: 20px 25px;
            border-radius: 25px;
            position: relative;
            word-wrap: break-word;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .message.user .message-bubble {
            background: rgba(102, 126, 234, 0.3);
            color: rgba(255, 255, 255, 0.95);
            border-bottom-left-radius: 8px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .message.bot .message-bubble {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            border-bottom-right-radius: 8px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 8px;
            color: rgba(255, 255, 255, 0.7);
        }

        .typing-indicator {
            display: none;
            text-align: right;
            margin: 25px;
        }

        .typing-bubble {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            padding: 20px 25px;
            border-radius: 25px;
            border-bottom-right-radius: 8px;
            color: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .typing-dots {
            display: inline-flex;
            gap: 6px;
            margin-right: 15px;
        }

        .typing-dot {
            width: 10px;
            height: 10px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: typingGlass 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingGlass {
            0%, 60%, 100% { 
                transform: translateY(0);
                opacity: 0.4;
            }
            30% { 
                transform: translateY(-15px);
                opacity: 1;
            }
        }

        .input-area {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            padding: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-wrapper {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 18px 25px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 15px;
            resize: none;
            max-height: 120px;
            min-height: 55px;
            outline: none;
            transition: all 0.3s ease;
        }

        .message-input:focus {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .send-btn {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            width: 55px;
            height: 55px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        .send-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        /* Scrollbar Styling */
        .messages-area::-webkit-scrollbar {
            width: 8px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .demo-label {
            position: absolute;
            top: 15px;
            left: 25px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: rgba(255, 255, 255, 0.9);
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            z-index: 20;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 20px;
            }
            
            .message-bubble {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="chat-container">
        <div class="demo-label">Glassmorphism Style Demo</div>
        
        <div class="chat-header">
            <div class="chat-title">
                <div class="glow-icon">⚡</div>
                مساعد الطاقة الذكي
            </div>
            <button class="close-btn" onclick="window.close()">×</button>
        </div>

        <div class="messages-area" id="messagesArea">
            <div class="message bot">
                <div class="message-bubble">
                    مرحباً بك في عالم الطاقة الذكية! ✨
                    <br>أنا هنا لمساعدتك في تحسين استهلاك الطاقة وتوفير التكاليف
                </div>
                <div class="message-time">الآن</div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-bubble">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
                يحلل البيانات...
            </div>
        </div>

        <div class="input-area">
            <div class="input-wrapper">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="شاركني أسئلتك حول الطاقة..."
                    rows="1"
                ></textarea>
                <button class="send-btn" id="sendBtn" disabled>
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const messagesArea = document.getElementById('messagesArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            
            sendBtn.disabled = this.value.trim() === '';
        });

        // Send message on Enter
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendBtn.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';
            sendBtn.disabled = true;

            // Show typing indicator
            showTyping();

            // Simulate bot response
            setTimeout(() => {
                hideTyping();
                const responses = [
                    'رائع! يمكنني تحليل استهلاك الطاقة وتقديم حلول مخصصة لك 🔋',
                    'هذا سؤال ممتاز! دعني أقدم لك أحدث التقنيات في توفير الطاقة ⚡',
                    'بالتأكيد! إليك خطة شاملة لتحسين كفاءة الطاقة في مكانك 🌟',
                    'ممتاز! سأساعدك في إنشاء نظام طاقة ذكي ومستدام 🌱'
                ];
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage(randomResponse, 'bot');
            }, 2500);
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            messageDiv.innerHTML = `
                <div class="message-bubble">${text}</div>
                <div class="message-time">${timeString}</div>
            `;

            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        // Add demo messages
        setTimeout(() => {
            addMessage('كيف يمكنني تقليل فاتورة الكهرباء؟', 'user');
        }, 1500);

        setTimeout(() => {
            showTyping();
        }, 2500);

        setTimeout(() => {
            hideTyping();
            addMessage('إليك أفضل الطرق لتقليل فاتورة الكهرباء:\n\n✨ استخدم الألواح الشمسية\n⚡ اختر أجهزة موفرة للطاقة\n🌡️ اضبط التكييف على درجة مناسبة\n💡 استبدل المصابيح بـ LED\n🔌 افصل الأجهزة غير المستخدمة', 'bot');
        }, 4500);
    </script>
</body>
</html>
