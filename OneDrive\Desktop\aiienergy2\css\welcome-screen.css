/* ===== ENERGY.AI WELCOME SCREEN STYLES ===== */

/* Welcome Screen Base */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.8s ease;
    overflow: hidden;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Welcome Container */
.welcome-container {
    text-align: center;
    position: relative;
    z-index: 10;
    animation: welcomeFadeIn 1.5s ease-out;
}

.welcome-logo-section {
    margin-bottom: 3rem;
}

/* Enhanced Logo */
.large-logo {
    width: 120px !important;
    height: 120px !important;
    margin: 0 auto 2rem;
    position: relative;
    animation: logoFloat 3s ease-in-out infinite;
}

.large-logo::before {
    font-size: 4rem !important;
    animation: logoPulse 2s ease-in-out infinite;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 140px;
    height: 140px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 114, 0, 0.3) 0%, transparent 70%);
    animation: glowPulse 2s ease-in-out infinite alternate;
    z-index: -1;
}

[data-theme="light"] .logo-glow {
    background: radial-gradient(circle, rgba(25, 118, 210, 0.3) 0%, transparent 70%);
}

/* Welcome Typography */
.welcome-title {
    font-size: 3.8rem;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 0.8rem;
    opacity: 0;
    animation: textSlideUp 1s ease-out 0.5s forwards;
    letter-spacing: 3px;
    font-family: 'Roboto', sans-serif;
}

.brand-name {
    font-size: 5rem;
    font-weight: 700;
    background: var(--energy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    opacity: 0;
    animation: textSlideUp 1s ease-out 0.8s forwards;
    letter-spacing: 3px;
    font-family: 'Roboto', sans-serif;
}

.welcome-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    font-weight: 300;
    opacity: 0;
    animation: textSlideUp 1s ease-out 1.1s forwards;
    letter-spacing: 1px;
    font-family: 'Roboto', sans-serif;
}

/* Energy Particles Animation */
.energy-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.energy-particles::before,
.energy-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--secondary-color);
    border-radius: 50%;
    animation: particleFloat 4s ease-in-out infinite;
}

.energy-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.energy-particles::after {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

[data-theme="light"] .energy-particles::before,
[data-theme="light"] .energy-particles::after {
    background: var(--primary-color);
}

/* Floating Particles */
.floating-particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

[data-theme="light"] .floating-particle {
    background: var(--primary-color) !important;
}

/* Progress Bar */
.loading-progress {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    margin: 2rem auto 0;
    overflow: hidden;
    opacity: 0;
    animation: fadeIn 1s ease-out 1.5s forwards;
}

[data-theme="light"] .loading-progress {
    background: rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: var(--energy-gradient);
    border-radius: 2px;
    width: 0%;
    animation: progressLoad 2s ease-out 1.5s forwards;
}

/* Welcome Animations */
@keyframes welcomeFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes logoPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes glowPulse {
    0% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes textSlideUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes progressLoad {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}



/* Responsive Design */
@media (max-width: 768px) {
    .welcome-title {
        font-size: 2.8rem;
        letter-spacing: 2px;
    }
    
    .brand-name {
        font-size: 3.5rem;
    }
    
    .welcome-subtitle {
        font-size: 1.2rem;
        padding: 0 1rem;
    }
    
    .large-logo {
        width: 80px !important;
        height: 80px !important;
    }
    
    .large-logo::before {
        font-size: 2.5rem !important;
    }
    
    .logo-glow {
        width: 100px;
        height: 100px;
    }
    
    .loading-progress {
        width: 250px;
    }
}

@media (max-width: 480px) {
    .welcome-title {
        font-size: 2.2rem;
        letter-spacing: 1px;
    }
    
    .brand-name {
        font-size: 2.8rem;
    }
    
    .welcome-subtitle {
        font-size: 1rem;
    }
    
    .large-logo {
        width: 60px !important;
        height: 60px !important;
    }
    
    .large-logo::before {
        font-size: 2rem !important;
    }
    
    .loading-progress {
        width: 200px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .welcome-title,
    .welcome-subtitle {
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    }
    
    .logo-glow {
        opacity: 0.8;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .welcome-container,
    .large-logo,
    .logo-glow,
    .welcome-title,
    .brand-name,
    .welcome-subtitle,
    .energy-particles::before,
    .energy-particles::after,
    .floating-particle,
    .loading-progress,
    .progress-bar {
        animation: none !important;
    }
    
    .welcome-title,
    .brand-name,
    .welcome-subtitle,
    .loading-progress {
        opacity: 1 !important;
    }
    
    .progress-bar {
        width: 100% !important;
    }
}
