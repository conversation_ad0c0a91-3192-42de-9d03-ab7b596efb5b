/* Authentication System Styles */

/* Auth Modal */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.auth-modal.active {
    opacity: 1;
    visibility: visible;
}

.auth-modal.hidden {
    opacity: 0;
    visibility: hidden;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.auth-container {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 35px;
    max-width: 420px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.auth-modal.active .auth-container {
    transform: scale(1);
}

.auth-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.auth-title {
    font-size: 22px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
    margin: 0;
}

.auth-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-close:hover {
    color: rgba(255, 255, 255, 1);
    background: rgba(255, 255, 255, 0.1);
}

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 5px;
}

.auth-tab {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* ===== AUTH TAB ACTIVE ===== */

/* Default/Dark Theme */
.auth-tab.active {
    background: var(--secondary-color); /* Orange for dark theme */
    color: white;
    box-shadow: 0 2px 10px rgba(255, 114, 0, 0.3);
}

/* Light Theme */
[data-theme="light"] .auth-tab.active {
    background: var(--primary-color); /* Blue for light theme */
    box-shadow: 0 2px 10px rgba(25, 118, 210, 0.3);
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 11px 14px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-sizing: border-box;
}

/* ===== FORM INPUT FOCUS ===== */

/* Default/Dark Theme */
.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color); /* Orange for dark theme */
    box-shadow: 0 0 0 3px rgba(255, 114, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

/* Light Theme */
[data-theme="light"] .form-group input:focus {
    border-color: var(--primary-color); /* Blue for light theme */
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 13px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
}

.checkbox-label input {
    margin-right: 6px;
    width: auto;
}

/* ===== FORGOT PASSWORD LINK ===== */

/* Default/Dark Theme */
.forgot-password {
    color: var(--secondary-color); /* Orange for dark theme */
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.forgot-password:hover {
    color: rgba(255, 114, 0, 0.8);
    text-decoration: underline;
}

/* Light Theme */
[data-theme="light"] .forgot-password {
    color: var(--primary-color); /* Blue for light theme */
}

[data-theme="light"] .forgot-password:hover {
    color: rgba(25, 118, 210, 0.8);
}

.auth-btn {
    width: 100%;
    padding: 12px 18px;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 12px;
}

/* ===== AUTH BUTTON PRIMARY ===== */

/* Default/Dark Theme */
.auth-btn.primary {
    background: var(--secondary-color); /* Orange for dark theme */
    color: white;
    box-shadow: 0 4px 15px rgba(255, 114, 0, 0.3);
}

.auth-btn.primary:hover {
    background: rgba(255, 114, 0, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 114, 0, 0.4);
}

/* Light Theme */
[data-theme="light"] .auth-btn.primary {
    background: var(--primary-color); /* Blue for light theme */
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .auth-btn.primary:hover {
    background: rgba(25, 118, 210, 0.9);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

.auth-btn.danger {
    background: #ff4444;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.auth-btn.danger:hover {
    background: rgba(255, 68, 68, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
}

.auth-divider {
    text-align: center;
    margin: 18px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
}

.auth-divider span {
    background: rgba(255, 255, 255, 0.1);
    padding: 0 12px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    position: relative;
}

.social-login {
    margin-top: 15px;
}

.social-btn {
    width: 100%;
    padding: 11px 18px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.social-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.social-btn ion-icon {
    font-size: 18px;
}

.auth-status {
    margin-top: 20px;
    padding: 12px 15px;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    display: none;
}

.auth-status.success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.3);
    color: #4CAF50;
}

.auth-status.error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #f44336;
}

/* ===== AUTH STATUS LOADING ===== */

/* Default/Dark Theme */
.auth-status.loading {
    background: rgba(255, 114, 0, 0.2);
    border: 1px solid rgba(255, 114, 0, 0.3);
    color: var(--secondary-color); /* Orange for dark theme */
}

/* Light Theme */
[data-theme="light"] .auth-status.loading {
    background: rgba(25, 118, 210, 0.2);
    border: 1px solid rgba(25, 118, 210, 0.3);
    color: var(--primary-color); /* Blue for light theme */
}

/* Dashboard Modal */
.dashboard-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.dashboard-modal.active {
    opacity: 1;
    visibility: visible;
}

.dashboard-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.dashboard-container {
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
}

.dashboard-modal.active .dashboard-container {
    transform: scale(1);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* ===== USER AVATAR ===== */

/* Default/Dark Theme */
.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--secondary-color); /* Orange for dark theme */
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

/* Light Theme */
[data-theme="light"] .user-avatar {
    background: var(--primary-color); /* Blue for light theme */
}

.user-info h3 {
    margin: 0;
    color: rgba(255, 255, 255, 0.95);
    font-size: 18px;
    font-weight: 600;
}

.user-info p {
    margin: 5px 0 0 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.dashboard-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.dashboard-close:hover {
    color: rgba(255, 255, 255, 1);
    background: rgba(255, 255, 255, 0.1);
}

.dashboard-nav {
    display: flex;
    padding: 0 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    overflow-x: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.nav-item:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.05);
}

/* ===== NAV ITEM ACTIVE ===== */

/* Default/Dark Theme */
.nav-item.active {
    color: var(--secondary-color); /* Orange for dark theme */
    border-bottom-color: var(--secondary-color);
}

/* Light Theme */
[data-theme="light"] .nav-item.active {
    color: var(--primary-color); /* Blue for light theme */
    border-bottom-color: var(--primary-color);
}

.nav-item ion-icon {
    font-size: 18px;
}

.dashboard-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.dashboard-section h3 {
    margin: 0 0 25px 0;
    color: rgba(255, 255, 255, 0.95);
    font-size: 24px;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28px;
}

.stat-info h4 {
    margin: 0 0 8px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
}

.stat-value {
    margin: 0;
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 700;
}

.energy-chart {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.analytics-content,
.settings-content {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
}

/* Light Theme Support */
[data-theme="light"] .auth-container,
[data-theme="light"] .dashboard-container {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .auth-title,
[data-theme="light"] .user-info h3,
[data-theme="light"] .dashboard-section h3 {
    color: rgba(0, 0, 0, 0.9);
}

[data-theme="light"] .auth-close,
[data-theme="light"] .dashboard-close {
    color: rgba(0, 0, 0, 0.7);
}

[data-theme="light"] .form-group label,
[data-theme="light"] .user-info p {
    color: rgba(0, 0, 0, 0.8);
}

[data-theme="light"] .form-group input {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.2);
    color: rgba(0, 0, 0, 0.9);
}

[data-theme="light"] .stat-card,
[data-theme="light"] .energy-chart,
[data-theme="light"] .analytics-content,
[data-theme="light"] .settings-content {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 25px 18px;
        margin: 15px;
        max-width: 380px;
    }

    .auth-title {
        font-size: 20px;
    }

    .form-group {
        margin-bottom: 14px;
    }

    .form-group input {
        padding: 10px 12px;
        font-size: 14px;
    }

    .auth-btn {
        padding: 11px 16px;
        font-size: 14px;
    }

    .social-btn {
        padding: 10px 16px;
        font-size: 12px;
    }

    .dashboard-container {
        margin: 10px;
        max-height: 95vh;
    }

    .dashboard-header {
        padding: 20px;
    }

    .dashboard-nav {
        padding: 0 20px;
    }

    .dashboard-content {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .nav-item {
        padding: 12px 15px;
        font-size: 13px;
    }

    .nav-item ion-icon {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 20px 12px;
        margin: 10px;
        max-width: 340px;
    }

    .auth-title {
        font-size: 18px;
    }

    .auth-header {
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 12px;
    }

    .form-group label {
        font-size: 12px;
        margin-bottom: 4px;
    }

    .form-group input {
        padding: 9px 10px;
        font-size: 13px;
    }

    .form-options {
        margin-bottom: 16px;
        font-size: 12px;
    }

    .auth-btn {
        padding: 10px 14px;
        font-size: 13px;
        margin-bottom: 10px;
    }

    .auth-divider {
        margin: 14px 0;
    }

    .auth-divider span {
        font-size: 12px;
        padding: 0 10px;
    }

    .social-btn {
        padding: 9px 14px;
        font-size: 12px;
        gap: 6px;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .dashboard-nav {
        justify-content: center;
    }

    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }
}

/* أنماط خاصة للوضع الفاتح - نافذة المصادقة شفافة */
[data-theme="light"] .auth-container {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(30px) !important;
    -webkit-backdrop-filter: blur(30px) !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

[data-theme="light"] .auth-title {
    color: #333 !important;
}

[data-theme="light"] .auth-close {
    color: #666 !important;
}

[data-theme="light"] .auth-close:hover {
    color: #1976D2 !important;
    background: rgba(25, 118, 210, 0.1) !important;
}

[data-theme="light"] .auth-tabs {
    background: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="light"] .auth-tab {
    color: #666 !important;
}

[data-theme="light"] .auth-tab.active {
    background: #1976D2 !important;
    color: white !important;
    box-shadow: 0 2px 10px rgba(25, 118, 210, 0.3) !important;
}

[data-theme="light"] .form-group label {
    color: #1976D2 !important;
}

[data-theme="light"] .form-group input {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    color: #333 !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

[data-theme="light"] .form-group input:focus {
    border-color: #1976D2 !important;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2) !important;
    background: rgba(255, 255, 255, 0.3) !important;
}

[data-theme="light"] .form-group input::placeholder {
    color: rgba(51, 51, 51, 0.6) !important;
}

[data-theme="light"] .checkbox-label {
    color: #666 !important;
}

[data-theme="light"] .forgot-password {
    color: #1976D2 !important;
}

[data-theme="light"] .forgot-password:hover {
    color: #0D47A1 !important;
}

[data-theme="light"] .auth-btn.primary {
    background: #1976D2 !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3) !important;
}

[data-theme="light"] .auth-btn.primary:hover {
    background: #0D47A1 !important;
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4) !important;
}
