{"name": "@gar/promisify", "version": "1.1.3", "description": "Promisify an entire class or object", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/wraithgar/gar-promisify.git"}, "scripts": {"lint": "standard", "lint:fix": "standard --fix", "test": "lab -a @hapi/code -t 100", "posttest": "npm run lint"}, "files": ["index.js"], "keywords": ["promisify", "all", "class", "object"], "author": "Gar <<EMAIL>>", "license": "MIT", "devDependencies": {"@hapi/code": "^8.0.1", "@hapi/lab": "^24.1.0", "standard": "^16.0.3"}}