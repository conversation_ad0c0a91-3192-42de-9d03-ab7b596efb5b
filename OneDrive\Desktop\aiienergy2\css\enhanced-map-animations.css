/* ===== ENHANCED MAP ANIMATIONS ===== */

/* رسوم متحركة للخرائط المتقدمة */
@keyframes mapZoomIn {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes mapSlideIn {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes mapGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 114, 0, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(255, 114, 0, 0.6);
    }
}

@keyframes markerBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes markerPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes controlsSlideIn {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toolbarSlideDown {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes buttonHover {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1.05);
    }
}

@keyframes loadingSpinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}

/* فئات الرسوم المتحركة */
.map-zoom-in {
    animation: mapZoomIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.map-slide-in {
    animation: mapSlideIn 0.8s ease-out;
}

.map-glow {
    animation: mapGlow 2s ease-in-out infinite;
}

.marker-bounce {
    animation: markerBounce 1s ease-in-out;
}

.marker-pulse {
    animation: markerPulse 1.5s ease-in-out infinite;
}

.controls-slide-in {
    animation: controlsSlideIn 0.5s ease-out;
}

.toolbar-slide-down {
    animation: toolbarSlideDown 0.4s ease-out;
}

.button-hover {
    animation: buttonHover 0.3s ease-out;
}

.loading-spinner {
    animation: loadingSpinner 1s linear infinite;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInFromRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInFromLeft 0.5s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 114, 0, 0.3);
    transform: translate(-50%, -50%);
    animation: ripple 0.6s ease-out;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.float {
    animation: float 3s ease-in-out infinite;
}

.rotate {
    animation: rotate 2s linear infinite;
}

.heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* تأثيرات التمرير */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 114, 0, 0.5);
}

/* تأثيرات الانتقال المتقدمة */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.elastic-transition {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.bounce-transition {
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* تأثيرات الظهور والاختفاء */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s ease-in forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.fade-out {
    opacity: 1;
    animation: fadeOut 0.5s ease-out forwards;
}

@keyframes fadeOut {
    to {
        opacity: 0;
    }
}

.slide-up {
    transform: translateY(100%);
    animation: slideUp 0.5s ease-out forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
    }
}

.slide-down {
    transform: translateY(-100%);
    animation: slideDown 0.5s ease-out forwards;
}

@keyframes slideDown {
    to {
        transform: translateY(0);
    }
}

/* تأثيرات التركيز */
.focus-ring {
    position: relative;
}

.focus-ring:focus::after {
    content: "";
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid rgba(255, 114, 0, 0.5);
    border-radius: inherit;
    animation: focusRing 0.3s ease-out;
}

@keyframes focusRing {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* تأثيرات التحميل المتقدمة */
.skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.progress-bar-animated {
    background: linear-gradient(90deg, #ff7200, #ff9500, #ff7200);
    background-size: 200% 100%;
    animation: progressBarAnimation 2s linear infinite;
}

@keyframes progressBarAnimation {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* تحسينات الأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

.reduce-motion {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

/* استعلامات الوسائط للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== أدوات التحكم المصغرة ===== */
.compact-controls {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1000;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.9));
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: slideInLeft 0.4s ease-out;
    max-width: 200px;
}

.compact-panel {
    overflow: hidden;
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    cursor: pointer;
    border-radius: 12px 12px 0 0;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.panel-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.panel-icon {
    font-size: 14px;
}

.panel-title {
    flex: 1;
    margin-left: 8px;
}

.collapse-icon {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.panel-content {
    padding: 12px;
    transition: all 0.3s ease;
    max-height: 400px;
    overflow: hidden;
}

.panel-content.collapsed {
    max-height: 0;
    padding: 0 12px;
}

.compact-section {
    margin-bottom: 12px;
}

.compact-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 11px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.compact-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.compact-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.compact-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.compact-btn.active {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    transform: scale(1.05);
}

.compact-toggles {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.mini-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.mini-toggle input {
    display: none;
}

.mini-slider {
    width: 32px;
    height: 32px;
    background: #ddd;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.mini-toggle input:checked + .mini-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.05);
}

.compact-tools {
    display: flex;
    gap: 4px;
}

.tool-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
    border: none;
    padding: 6px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}
